<!-- <route lang="yaml">
meta:
  title: 权限管理
  icon: i-ep:lock
  constant: true
</route> -->

<script setup lang="ts">
import debounce from 'lodash/debounce'

type PermType = {
  MENU: number
  BUTTON: number
  API: number
}

const permType: PermType = {
  MENU: 1,
  BUTTON: 2,
  API: 3,
}

type PermItem = {
  id?: number
  idx?: number | null
  pid?: number | null
  pname: string
  ptype: number
  pval: string
  leaf?: boolean
  parent?: string | null
  children?: PermItem[]
}

// mock api layer placeholders; replace with real apis if available
async function listAllPermissions() {
  return {
    btnPermMap: {},
    permMap: {
      [permType.MENU]: [],
      [permType.API]: [],
    },
  }
}
async function addPerm(_data: PermItem) {}
async function updatePerm(_data: PermItem) {}
async function deletePerm(_id?: number) {}
async function syncMenuPerms(_list: PermItem[]) {}
async function syncApiPerms(_list: PermItem[]) {}

// tree helpers: adapt from your project as needed
function generateMenuPermissionTree(): PermItem[] {
  // Placeholder: build from actual routes/metadata in your project
  return [
    {
      pname: '系统管理',
      pval: 'm:system',
      ptype: permType.MENU,
      children: [
        { pname: '用户管理', pval: 'm:system:user', ptype: permType.MENU },
        { pname: '角色管理', pval: 'm:system:role', ptype: permType.MENU },
      ],
    },
  ]
}

function mapToButtonPermissionTree(btnPermMap: Record<string, PermItem[]>, tree: PermItem[]): PermItem[] {
  return tree.map((node) => {
    const children: PermItem[] = []
    if (node.children && node.children.length) {
      children.push(...mapToButtonPermissionTree(btnPermMap, node.children))
    }
    const btns = btnPermMap[node.pval] || []
    children.push(...btns.map(b => ({ ...b, ptype: permType.BUTTON })))
    return { ...node, children }
  })
}

const btnPermPrefix = ref('b:')
const filterPlaceholderText = ref('输入权限名称、权限值过滤')

const menuPermValSet = ref(new Set<string>())
const apiPermValSet = ref(new Set<string>())
const btnPermMap = ref<Record<string, PermItem[]>>({})

const menuPermissionTree = ref<PermItem[]>([])
const buttonPermissionTree = ref<PermItem[]>([])
const apiPermissionTree = ref<PermItem[]>([])

const filterMenuPermText = ref('')
const filterButtonPermText = ref('')
const filterApiPermText = ref('')

const menuPermTreeRef = ref<any>()
const buttonPermTreeRef = ref<any>()
const apiPermTreeRef = ref<any>()

const treeProps = {
  label: 'pname',
  children: 'children',
}

const dialogFormVisible = ref(false)
const dialogStatus = ref<'addButton' | 'updateButton' | ''>('')
const textMap: Record<string, string> = {
  addButton: '添加按钮权限',
  updateButton: '更新按钮权限',
}
const temp = reactive<PermItem>({
  idx: null,
  pid: null,
  pname: '',
  ptype: permType.BUTTON,
  pval: '',
  leaf: undefined,
  parent: '',
})

function resetTemp() {
  temp.idx = null
  temp.pid = null
  temp.pname = ''
  temp.ptype = permType.BUTTON
  temp.pval = ''
  temp.leaf = undefined
  temp.parent = ''
}

function filterNode(value: string, data: PermItem) {
  if (!value) return true
  return data.pname.includes(value) || data.pval.includes(value)
}

const doFilterMenu = debounce((val: string) => menuPermTreeRef.value?.filter(val), 600)
const doFilterButton = debounce((val: string) => buttonPermTreeRef.value?.filter(val), 600)
const doFilterApi = debounce((val: string) => apiPermTreeRef.value?.filter(val), 600)

watch(filterMenuPermText, (val) => doFilterMenu(val))
watch(filterButtonPermText, (val) => doFilterButton(val))
watch(filterApiPermText, (val) => doFilterApi(val))

async function initData() {
  const res = await listAllPermissions()
  btnPermMap.value = res.btnPermMap || {}
  const permMap = res.permMap || {}
  const menuPermList: PermItem[] = permMap[permType.MENU] || []
  menuPermValSet.value = new Set(menuPermList.map(p => p.pval))
  const apiPermList: PermItem[] = permMap[permType.API] || []
  apiPermValSet.value = new Set(apiPermList.map(p => p.pval))
  menuPermissionTree.value = generateMenuPermissionTree()
  const menuPermissionTreeCopy = generateMenuPermissionTree()
  buttonPermissionTree.value = mapToButtonPermissionTree(btnPermMap.value, menuPermissionTreeCopy)
  loadApiButtonPermissionTree()
}

function loadApiButtonPermissionTree() {
  apiPermissionTree.value = []
}

function handleAddButton(data: PermItem) {
  dialogStatus.value = 'addButton'
  resetTemp()
  temp.ptype = permType.BUTTON
  temp.parent = data.pval
  dialogFormVisible.value = true
}

async function addButton() {
  const data = { ...temp }
  data.pval = `${btnPermPrefix.value}${data.pval}`
  await addPerm(data)
  dialogFormVisible.value = false
  await initData()
}

function handleUpdateButton(data: PermItem) {
  dialogStatus.value = 'updateButton'
  Object.assign(temp, data)
  dialogFormVisible.value = true
}

async function updateButton() {
  const data = { ...temp }
  await updatePerm(data)
  dialogFormVisible.value = false
  await initData()
}

async function handleDeleleButton(data: PermItem) {
  await deletePerm(data.id)
  await initData()
}

function permissionTreeToList(list: PermItem[], tree: PermItem[]) {
  tree.forEach((perm) => {
    const t: PermItem = { ...perm, children: [] }
    if (perm.children && perm.children.length > 0) {
      t.leaf = false
      permissionTreeToList(list, perm.children)
    }
    else {
      t.leaf = true
    }
    list.push(t)
  })
}

async function handleSyncMenuPermissionData() {
  const list: PermItem[] = []
  permissionTreeToList(list, menuPermissionTree.value)
  await syncMenuPerms(list)
  await initData()
}

async function handleSyncApiPermissionData() {
  const list: PermItem[] = []
  permissionTreeToList(list, apiPermissionTree.value)
  await syncApiPerms(list)
  await initData()
}

onMounted(() => {
  initData()
})
</script>

<template>
  <div class="app-container">
    <ElRow :gutter="20">
      <ElCol :span="12">
        <ElCard class="box-card">
          <template #header>
            <div class="title-box">
              <span>
                <ElTag type="success">菜单</ElTag>
                &nbsp;权限元数据
              </span>
              <ElTooltip content="同步菜单权限数据" placement="top">
                <ElButton style="font-size: 25px" type="text" @click="handleSyncMenuPermissionData" :icon="'i-ep:refresh'" circle></ElButton>
              </ElTooltip>
            </div>
            <span class="tips-text">
              提示：菜单权限由页面路由定义，不提供任何编辑功能，只能执行将权限数据同步到数据库的操作。 菜单权限值建议使用前缀&nbsp;
              <ElTag size="small" type="success">m:</ElTag>
            </span>
          </template>
          <ElInput class="mgb-15" :placeholder="filterPlaceholderText" v-model="filterMenuPermText" />
          <ElTree
            ref="menuPermTreeRef"
            :filter-node-method="filterNode"
            :data="menuPermissionTree"
            :props="treeProps"
            node-key="pval"
            default-expand-all
            :expand-on-click-node="false"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>
                  <span class="mgl-10">{{ data.pname }}</span>
                  <span class="mgl-10 tips-text">{{ data.pval }}</span>
                  <ElTag class="mgl-10" type="success" size="small">菜单</ElTag>
                  <ElTag v-if="!menuPermValSet.has(data.pval)" class="mgl-10" type="danger" size="small">未同步</ElTag>
                </span>
              </span>
            </template>
          </ElTree>
        </ElCard>
      </ElCol>

      <ElCol :span="12">
        <ElCard class="box-card">
          <template #header>
            <div class="title-box" style="padding-top: 10px; padding-bottom: 13px">
              <span>
                <ElTag type="warning">按钮</ElTag>
                &nbsp;权限元数据
              </span>
            </div>
            <span class="tips-text">
              提示：按钮权限是依附在菜单权限下的，这样能帮助您更好区分相似的按钮权限。 按钮权限值建议使用前缀&nbsp;
              <ElTag size="small" type="warning">b:</ElTag>
            </span>
          </template>
          <ElInput class="mgb-15" :placeholder="filterPlaceholderText" v-model="filterButtonPermText" />
          <ElTree
            ref="buttonPermTreeRef"
            :filter-node-method="filterNode"
            :data="buttonPermissionTree"
            :props="treeProps"
            node-key="pval"
            default-expand-all
            :expand-on-click-node="false"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>
                  <span class="mgl-10">{{ data.pname }}</span>
                  <span class="mgl-10 tips-text">{{ data.pval }}</span>
                  <ElTag class="mgl-10" v-if="data.ptype == permType.MENU" type="success" size="small">菜单</ElTag>
                  <ElTag class="mgl-10" v-else-if="data.ptype == permType.BUTTON" type="warning" size="small">按钮</ElTag>
                </span>
                <ElTooltip v-if="data.ptype == permType.MENU" style="margin-right: 80px" content="添加按钮权限" placement="top">
                  <ElButton type="text" size="small" :icon="'i-ep:plus'" @click="handleAddButton(data)"></ElButton>
                </ElTooltip>

                <span v-if="data.ptype == permType.BUTTON">
                  <ElTooltip content="更新" placement="top">
                    <ElButton class="update-btn" type="text" size="small" :icon="'i-ep:edit'" @click="handleUpdateButton(data)"></ElButton>
                  </ElTooltip>
                  <ElTooltip content="删除" placement="top">
                    <ElButton class="delete-btn" type="text" size="small" :icon="'i-ep:delete'" @click="handleDeleleButton(data)"></ElButton>
                  </ElTooltip>
                </span>
              </span>
            </template>
          </ElTree>
        </ElCard>
      </ElCol>
    </ElRow>

    <ElDialog :title="textMap[dialogStatus]" v-model="dialogFormVisible" width="30%" :close-on-click-modal="false">
      <ElForm ref="dataForm" label-position="top">
        <ElFormItem label="权限名">
          <ElInput v-model="temp.pname" placeholder="例如：用户管理、添加用户" />
        </ElFormItem>
        <ElFormItem label="权限值">
          <ElInput v-model="temp.pval" :placeholder="dialogStatus === 'addButton' ? '已自动加上前缀\'b:\'，您只需填剩余部分，如：user:manage、user:add' : ''" :disabled="dialogStatus === 'updateButton'">
            <template #prepend v-if="dialogStatus === 'addButton'">{{ btnPermPrefix }}</template>
          </ElInput>
          <span class="tips-text">
            提示：接口权限值建议使用前缀&nbsp;
            <ElTag size="small" type="warning">b:</ElTag>
          </span>
        </ElFormItem>
        <ElFormItem label="父级权限值">
          <ElInput v-model="temp.parent" :disabled="true" />
        </ElFormItem>
        <ElFormItem label="权限类型" v-if="dialogStatus">
          <ElSelect v-model="temp.ptype" :disabled="true">
            <ElOption label="按钮" :value="permType.BUTTON" />
          </ElSelect>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="dialogFormVisible = false">取消</ElButton>
        <ElButton v-if="dialogStatus === 'addButton'" type="primary" @click="addButton">确定</ElButton>
        <ElButton v-if="dialogStatus === 'updateButton'" type="primary" @click="updateButton">确定</ElButton>
      </template>
    </ElDialog>
  </div>
  
</template>

<style scoped>
.box-card {
  width: 100%;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tips-text {
  font-size: 14px;
  color: #909399;
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-btn {
  margin-left: 20px;
}

.delete-btn {
  margin-left: 20px;
  color: #f56c6c;
}

.mgl-10 { margin-left: 10px; }
.mgb-15 { margin-bottom: 15px; }
</style>


